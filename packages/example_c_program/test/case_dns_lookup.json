{"name": "DNS解析测试", "description": "测试DNS解析功能", "setup": [{"action": "wait_attach", "processes": ["example_client"]}], "cases": [{"name": "DNS解析", "targets": [{"process": "example_client", "aspects": ["resolve_aspect"], "breakpoints": {"functions": ["network_connect"]}, "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["***********"]}]}, {"function": "network_connect", "return": {"type": "int", "values": [10]}}]}], "verify": {"expect_debug_log": {"type": "result_ip", "message": "***********"}}}], "cleanup": [{"exit": "example_client"}]}