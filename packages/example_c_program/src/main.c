/**
 * @file main.c
 * @brief 示例程序主文件
 *
 * 本文件演示了如何使用libtestsocket库进行测试。
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <libtestsocket.h>
#include "network.h"
#include "mock_wrapper.h"


// 全局变量
static int g_running = 1;
static int g_connection = -1;

/**
 * 信号处理函数
 */
static void signal_handler(int sig)
{
    (void) sig;
    g_running = 0;
}

/**
 * 设置信号处理
 */
static void setup_signals(void)
{
    struct sigaction sa;
    
    memset(&sa, 0, sizeof(sa));
    sa.sa_handler = signal_handler;
    sigaction(SIGINT, &sa, NULL);
    sigaction(SIGTERM, &sa, NULL);
}

/**
 * 连接到服务器
 */
static int connect_to_server(const char* host, int port)
{
    int sockfd;
    char ip[64] = {0};

    // 设置观察点
    TEST_POINT(connect_start, (const char**)&host, (int*)&port);
    
    // 解析域名
    if (network_resolve(host, ip, sizeof(ip)) < 0) {
        fprintf(stderr, "Failed to resolve hostname: %s\n", host);
        return -1;
    }
    printf("Connecting to %s:%d...\n", ip, port);

    TEST_LOG("result_ip", ip);

    // 连接到服务器
    sockfd = network_connect(ip, port);
    if (sockfd < 0) {
        fprintf(stderr, "Failed to connect to server\n");
        return -1;
    }
    
    // 设置观察点
    TEST_POINT(connect_end, (int)sockfd);
    
    printf("Connected to server, sockfd=%d\n", sockfd);
    
    return sockfd;
}

/**
 * 发送消息
 */
static int send_message(int sockfd, const char* message)
{
    int ret;
    
    printf("Sending message: %s\n", message);
    
    // 设置观察点
    TEST_POINT(send_start, (int)sockfd, (const char*)message);
    
    // 发送消息
    ret = network_send(sockfd, message, strlen(message));
    if (ret < 0) {
        fprintf(stderr, "Failed to send message\n");
        return -1;
    }
    
    // 设置观察点
    TEST_POINT(send_end, (int*)&ret);
    
    printf("Sent %d bytes\n", ret);
    
    return ret;
}

/**
 * 接收消息
 */
static int receive_message(int sockfd, char* buffer, int size)
{
    int ret;
    
    printf("Receiving message...\n");
    
    // 设置观察点
    TEST_POINT(recv_start, (int)sockfd, (int *)&size);
    
    // 接收消息
    ret = network_recv(sockfd, buffer, size);
    if (ret < 0) {
        fprintf(stderr, "Failed to receive message\n");
        return -1;
    }
    
    // 确保字符串以NULL结尾
    if (ret < size) {
        buffer[ret] = '\0';
    } else {
        buffer[size - 1] = '\0';
    }
    
    // 设置观察点
    TEST_POINT(recv_end, (int*)&ret, (char **)&buffer);
    
    printf("Received %d bytes: %s\n", ret, buffer);
    
    return ret;
}

/**
 * 主循环
 */
static void main_loop(void)
{
    char buffer[1024];
    int counter = 0;
    
    while (g_running) {
        
        // 设置观察点
        TEST_POINT(main_loop, (int*)&counter);
        
        // 接收消息
        memset(buffer, 0, sizeof(buffer));
        int msg_len =receive_message(g_connection, buffer, sizeof(buffer));
        if (msg_len < 0) {
            break;
        }
        
        // 发送消息
        snprintf(buffer + msg_len, sizeof(buffer) - msg_len, "Hello from client, counter=%d", counter);
        if (send_message(g_connection, buffer) < 0) {
            break;
        }
        
        // 增加计数器
        counter++;
        
        // 处理测试事件
        TEST_PROCESS_EVENT();
        
        // 休眠
        sleep(1);
    }
}

/**
 * 主函数
 */
int main(int argc, char* argv[])
{
    const char* host = "localhost";
    int port = 8080;
    
    // 解析命令行参数
    if (argc > 1) {
        host = argv[1];
    }
    if (argc > 2) {
        port = atoi(argv[2]);
    }
    
    // 初始化测试环境
    TEST_INIT("example_client") ;

    
    // 设置信号处理
    setup_signals();
    
    // 连接到服务器
    g_connection = connect_to_server(host, port);
    if (g_connection < 0) {
        TEST_CLEANUP();
        return 1;
    }
    
    // 主循环
    main_loop();
    
    // 关闭连接
    if (g_connection >= 0) {
        network_close(g_connection);
        g_connection = -1;
    }
    
    // 清理测试环境
    TEST_CLEANUP();
    
    return 0;
}