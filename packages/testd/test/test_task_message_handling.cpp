/**
 * @file test_task_message_handling.cpp
 * @brief 测试任务消息处理的功能测试
 */

#include "test_case.hpp"
#include "message_system.hpp"
#include "message_handlers.hpp"
#include "context.hpp"
#include "json_protocol.hpp"
#include <iostream>
#include <memory>
#include <thread>
#include <chrono>

using namespace testd;

/**
 * @brief 模拟客户端连接
 */
class MockClientConnection : public ClientConnection {
public:
    MockClientConnection(const std::string& programName)
        : ClientConnection(-1, 0, programName) {}

    void simulateInit(const std::string& programName, int pid, std::shared_ptr<ClientConnection> self) {
        // 创建init消息
        struct json_object* initData = json_object_new_object();
        json_object_object_add(initData, "pid", json_object_new_int(pid));
        json_object_object_add(initData, "program", json_object_new_string(programName.c_str()));

        // 直接创建增强消息
        testd::EnhancedMessage msg;
        msg.type = "init";
        msg.id = "init_" + std::to_string(pid);
        msg.idInt = pid;
        msg.source = MessageSource::CLIENT;
        msg.clientConnection = self;
        msg.clientUUID = self->getUUID();  // 使用客户端UUID
        msg.priority = MessagePriority::HIGH;
        msg.addTag("lifecycle");
        msg.setData(initData);

        MessageSystem::getInstance().publish(msg);

        std::cout << "Simulated init message for " << programName
                  << " (PID: " << pid << ")" << std::endl;
    }
};

int main() {
    std::cout << "=== 测试任务消息处理功能测试 ===" << std::endl;
    
    // 1. 初始化消息系统
    MessageHandlerFactory::registerDefaultHandlers();
    
    // 2. 创建测试任务
    std::vector<std::string> expectedProcesses = {"nginx", "redis", "mysql"};
    auto waitTask = TestWaitAttachTask::create(
        "wait_for_processes", 
        "Wait for expected processes to attach",
        expectedProcesses,
        5000  // 5秒超时
    );
    
    std::cout << "\n--- 创建TestWaitAttachTask ---" << std::endl;
    std::cout << "期望进程: nginx, redis, mysql" << std::endl;
    
    // 3. 启动任务
    std::cout << "\n--- 启动任务 ---" << std::endl;
    int result = waitTask->execute();
    std::cout << "Task init result: " << result << std::endl;
    std::cout << "Task state: " << static_cast<int>(waitTask->getState()) << std::endl;
    
    // 4. 处理消息
    MessageSystem::getInstance().processMessages();
    
    // 5. 模拟非期望进程连接（应该被InitMessageHandler处理）
    std::cout << "\n--- 模拟非期望进程连接 ---" << std::endl;
    auto unexpectedClient = std::make_shared<MockClientConnection>("apache");
    Context::getInstance().addClient(unexpectedClient);
    unexpectedClient->simulateInit("apache", 1001, unexpectedClient);
    
    // 处理消息
    MessageSystem::getInstance().processMessages();
    
    // 6. 模拟期望进程连接（应该被TestTaskInitHandler处理）
    std::cout << "\n--- 模拟期望进程连接 ---" << std::endl;
    
    auto nginxClient = std::make_shared<MockClientConnection>("nginx");
    Context::getInstance().addClient(nginxClient);
    nginxClient->simulateInit("nginx", 1002, nginxClient);

    auto redisClient = std::make_shared<MockClientConnection>("redis");
    Context::getInstance().addClient(redisClient);
    redisClient->simulateInit("redis", 1003, redisClient);
    
    // 处理消息
    MessageSystem::getInstance().processMessages();
    
    // 7. 检查任务状态
    result = waitTask->execute();
    std::cout << "\nTask after 2 processes: " << result << std::endl;
    std::cout << "Task state: " << static_cast<int>(waitTask->getState()) << std::endl;
    
    // 8. 连接最后一个进程
    std::cout << "\n--- 连接最后一个进程 ---" << std::endl;
    auto mysqlClient = std::make_shared<MockClientConnection>("mysql");
    Context::getInstance().addClient(mysqlClient);
    mysqlClient->simulateInit("mysql", 1004, mysqlClient);
    
    // 处理消息
    MessageSystem::getInstance().processMessages();
    
    // 9. 检查任务完成状态
    result = waitTask->execute();
    std::cout << "\nTask after all processes: " << result << std::endl;
    std::cout << "Task state: " << static_cast<int>(waitTask->getState()) << std::endl;
    
    // 10. 测试超时情况
    std::cout << "\n--- 测试超时情况 ---" << std::endl;
    std::vector<std::string> timeoutProcesses = {"nonexistent"};
    auto timeoutTask = TestWaitAttachTask::create(
        "timeout_test", 
        "Test timeout functionality",
        timeoutProcesses,
        100  // 100ms超时
    );
    
    // 启动超时任务
    result = timeoutTask->execute();
    std::cout << "Timeout task init result: " << result << std::endl;
    
    // 处理消息
    MessageSystem::getInstance().processMessages();
    
    // 等待超时
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 检查超时
    result = timeoutTask->execute();
    std::cout << "Timeout task after timeout: " << result << std::endl;
    std::cout << "Timeout task state: " << static_cast<int>(timeoutTask->getState()) << std::endl;
    
    // 11. 清理
    std::cout << "\n--- 清理资源 ---" << std::endl;
    MessageSystem::getInstance().getRouter().clearSubscriptions();
    
    std::cout << "=== 测试完成 ===" << std::endl;
    return 0;
}
