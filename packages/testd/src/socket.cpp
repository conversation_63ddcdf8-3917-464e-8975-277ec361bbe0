/**
 * @file socket.cpp
 * @brief Socket通信模块实现 - C++11版本
 */

#include "socket.hpp"
#include "context.hpp"
#include <iostream>
#include <cstring>
#include <cstdio>
#include <unistd.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <string.h>
#include <errno.h>

namespace testd {

// Socket基类实现
Socket::Socket() : socketFd(-1) {
}

Socket::~Socket() {
    if (socketFd >= 0) {
        close(socketFd);
        socketFd = -1;
    }
}

bool Socket::setNonBlocking(int fd) {
    int flags = fcntl(fd, F_GETFL, 0);
    if (flags == -1) {
        std::cerr << "fcntl(F_GETFL) failed: " << strerror(errno) << std::endl;
        return false;
    }
    
    if (fcntl(fd, F_SETFL, flags | O_NONBLOCK) == -1) {
        std::cerr << "fcntl(F_SETFL) failed: " << strerror(errno) << std::endl;
        return false;
    }
    
    return true;
}

int Socket::sendJson(std::shared_ptr<ClientConnection> client, struct json_object* jsonObj) {
    if (!client || !jsonObj) {
        return -EINVAL;
    }
    
    const char* jsonStr = json_object_to_json_string(jsonObj);
    if (!jsonStr) {
        return -EINVAL;
    }
    
    size_t len = strlen(jsonStr);
    char buffer[128];
    snprintf(buffer, sizeof(buffer), "%zu\n", len);
    ssize_t written = send(client->getSocketFd(), buffer, strlen(buffer), 0);
    if (written < 0 || (size_t)written != strlen(buffer)) {
        std::cerr << "write failed: " << strerror(errno) << std::endl;
        return -errno;
    }
    
    ssize_t sent = send(client->getSocketFd(), jsonStr, len, 0);
    
    if (sent < 0) {
        std::cerr << "send failed: " << strerror(errno) << std::endl;
        return -errno;
    }

    buffer[0] = '\n';
    written = send(client->getSocketFd(), buffer, 1, 0);
    
    if (written < 0 || (size_t)written != 1) {
        std::cerr << "write failed: " << strerror(errno) << std::endl;
        return -errno;
    }
    
    if (Context::getInstance().getConfig().isVerbose()) {
        std::cout << "Sent to client " << client->getSocketFd() << ": " << jsonStr << std::endl;
    }
    
    return sent;
}

int Socket::recvFromClient(std::shared_ptr<ClientConnection> client, std::string &data) {
    if (!client) {
        return -EINVAL;
    }
    
    char buffer[UnixDomainSocket::BUFFER_SIZE];

    ssize_t received = recv(client->getSocketFd(), buffer, UnixDomainSocket::BUFFER_SIZE - 1, 0);
    if (received == 0) {
        // 客户端关闭连接
        return -ECONNRESET;
    }
    if (received < 0) {
        if (errno != EAGAIN && errno != EWOULDBLOCK) {
            std::cerr << "recv failed: " << strerror(errno) << std::endl;
            return -errno;
        }
        received = 0;
    }
    buffer[received] = '\0';
    data.append(buffer, received);
    if (Context::getInstance().getConfig().isVerbose()) {
        std::cout << "Received from client " << client->getSocketFd() << ": " << buffer << std::endl;
    }
    return received;
    
}

int Socket::parseJson(struct json_object **jsonObj, std::string &data) {
    if (!jsonObj) {
        return -EINVAL;
    }
    *jsonObj = nullptr;
    
    /*
        format:

        <pack_length>\n<content>\n
    */
    if (data.empty()) {
        return 0;
    }
    
    size_t newline_pos = data.find('\n');

    while (newline_pos = data.find('\n'), newline_pos != std::string::npos) {
        if (newline_pos == std::string::npos) {
            return -EAGAIN;
        }
        
        size_t pack_length = std::stoul(data.substr(0, newline_pos));
        if (pack_length == 0) {
            // 无效的包长度
            std::cerr << "Invalid pack length: " << data.substr(0, newline_pos) << std::endl;
            data = data.substr(newline_pos + 1);
            continue;
        }
        
        if (pack_length + newline_pos + 2 > data.size()) {
            return -EAGAIN;
        }
        
        std::string json_str = data.substr(newline_pos + 1, pack_length);
        data = data.substr(pack_length + newline_pos + 2);
        if (json_str.empty()) {
            return 0;
        }
        
        struct json_object* obj = json_tokener_parse(json_str.c_str());
        if (!obj) {
            std::cerr << "Invalid JSON: " << json_str << std::endl;
            return -EINVAL;
        }
        
        *jsonObj = obj;
        return pack_length + newline_pos + 2;
    }
    return 0;    
}

int Socket::sendToProcess(pid_t pid, struct json_object* jsonObj) {
    auto client = findClientByPid(pid);
    if (!client) {
        return -ENOENT;
    }
    
    return sendJson(client, jsonObj);
}

bool Socket::closeClient(std::shared_ptr<ClientConnection> client) {
    if (!client) {
        return false;
    }
    
    close(client->getSocketFd());
    
    // 从客户端列表中移除
    Context::getInstance().removeClient(client->getUUID());
    return true;
}

std::shared_ptr<ClientConnection> Socket::findClientByPid(pid_t pid) {
    const auto& clients = Context::getInstance().getClients();
    for (const auto& pair : clients) {
        auto client = pair.second;
        if (client->getPid() == pid) {
            return client;
        }
    }
    return nullptr;
}

std::shared_ptr<ClientConnection> Socket::findClientByFd(int fd) {
    const auto& clients = Context::getInstance().getClients();
    for (const auto& pair : clients) {
        auto client = pair.second;
        if (client->getSocketFd() == fd) {
            return client;
        }
    }
    return nullptr;
}

// UnixDomainSocket类实现
UnixDomainSocket::UnixDomainSocket() : Socket() {
}

UnixDomainSocket::~UnixDomainSocket() {
    if (socketFd >= 0) {
        close(socketFd);
        unlink(UNIX_SOCKET_PATH);
        socketFd = -1;
    }
}

bool UnixDomainSocket::init() {
    struct sockaddr_un addr;
    
    // 创建socket
    socketFd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (socketFd < 0) {
        std::cerr << "socket failed: " << strerror(errno) << std::endl;
        return false;
    }
    
    // 设置socket为非阻塞模式
    if (!setNonBlocking(socketFd)) {
        close(socketFd);
        socketFd = -1;
        return false;
    }
    
    // 创建目录
    std::string dirPath = std::string(UNIX_SOCKET_PATH).substr(0, std::string(UNIX_SOCKET_PATH).find_last_of('/'));
    mkdir(dirPath.c_str(), 0755);
    
    // 删除可能存在的旧socket文件
    unlink(UNIX_SOCKET_PATH);
    
    // 绑定地址
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, UNIX_SOCKET_PATH, sizeof(addr.sun_path) - 1);
    
    if (bind(socketFd, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        std::cerr << "bind failed: " << strerror(errno) << std::endl;
        close(socketFd);
        socketFd = -1;
        return false;
    }
    
    // 监听连接
    if (listen(socketFd, 5) < 0) {
        std::cerr << "listen failed: " << strerror(errno) << std::endl;
        close(socketFd);
        socketFd = -1;
        return false;
    }
    
    return true;
}

std::shared_ptr<ClientConnection> UnixDomainSocket::acceptConnection() {
    struct sockaddr_un addr;
    socklen_t addrLen = sizeof(addr);
    
    // 接受连接
    int clientFd = accept(socketFd, (struct sockaddr*)&addr, &addrLen);
    if (clientFd < 0) {
        if (errno == EAGAIN || errno == EWOULDBLOCK) {
            // 非阻塞模式下没有连接可接受
            return nullptr;
        }
        std::cerr << "accept failed: " << strerror(errno) << std::endl;
        return nullptr;
    }
    
    // 设置客户端socket为非阻塞模式
    if (!setNonBlocking(clientFd)) {
        close(clientFd);
        return nullptr;
    }
    
    // 创建客户端连接对象
    return std::make_shared<ClientConnection>(clientFd);
}

// TcpSocket类实现
TcpSocket::TcpSocket(int port) : Socket(), port(port) {
}

TcpSocket::~TcpSocket() {
    if (socketFd >= 0) {
        close(socketFd);
        socketFd = -1;
    }
}

bool TcpSocket::init() {
    struct sockaddr_in addr;
    int opt = 1;
    
    // 创建socket
    socketFd = socket(AF_INET, SOCK_STREAM, 0);
    if (socketFd < 0) {
        std::cerr << "socket failed: " << strerror(errno) << std::endl;
        return false;
    }
    
    // 设置socket选项
    if (setsockopt(socketFd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        std::cerr << "setsockopt failed: " << strerror(errno) << std::endl;
        close(socketFd);
        socketFd = -1;
        return false;
    }
    
    // 设置socket为非阻塞模式
    if (!setNonBlocking(socketFd)) {
        close(socketFd);
        socketFd = -1;
        return false;
    }
    
    // 绑定地址
    memset(&addr, 0, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = htonl(INADDR_ANY);
    addr.sin_port = htons(port);
    
    if (bind(socketFd, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        std::cerr << "bind failed: " << strerror(errno) << std::endl;
        close(socketFd);
        socketFd = -1;
        return false;
    }
    
    // 监听连接
    if (listen(socketFd, 5) < 0) {
        std::cerr << "listen failed: " << strerror(errno) << std::endl;
        close(socketFd);
        socketFd = -1;
        return false;
    }
    
    return true;
}

std::shared_ptr<ClientConnection> TcpSocket::acceptConnection() {
    struct sockaddr_in addr;
    socklen_t addrLen = sizeof(addr);
    
    // 接受连接
    int clientFd = accept(socketFd, (struct sockaddr*)&addr, &addrLen);
    if (clientFd < 0) {
        if (errno == EAGAIN || errno == EWOULDBLOCK) {
            // 非阻塞模式下没有连接可接受
            return nullptr;
        }
        std::cerr << "accept failed: " << strerror(errno) << std::endl;
        return nullptr;
    }
    
    // 设置客户端socket为非阻塞模式
    if (!setNonBlocking(clientFd)) {
        close(clientFd);
        return nullptr;
    }
    
    // 获取客户端IP地址
    char ip[INET_ADDRSTRLEN];
    inet_ntop(AF_INET, &addr.sin_addr, ip, sizeof(ip));
    
    // 创建客户端连接对象
    auto client = std::make_shared<ClientConnection>(clientFd, 0, std::string("tcp_client_") + ip);
    
    if (Context::getInstance().getConfig().isVerbose()) {
        std::cout << "New TCP client connected from " << ip << ":" << ntohs(addr.sin_port) 
                  << ", fd=" << clientFd << std::endl;
    }
    
    return client;
}

} // namespace testd
