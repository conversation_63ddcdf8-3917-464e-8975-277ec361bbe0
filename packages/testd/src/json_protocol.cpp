/**
 * @file json_protocol.cpp
 * @brief JSON协议处理模块实现 - C++11版本
 */

#include "json_protocol.hpp"
#include "context.hpp"
#include "test_case.hpp"
#include "message_system.hpp"
#include <json-c/json_object.h>
#include <sys/time.h>
#include <iostream>
#include <cstring>
#include <list>
#include "socket.hpp"

namespace testd {

/**
 * @brief RequestTask专用的响应处理器
 */
class RequestTaskResponseHandler : public MessageHandler {
public:
    RequestTaskResponseHandler(int expectedId, std::function<void(bool, const std::string&)> callback)
        : expectedId_(expectedId), callback_(callback) {}

    bool handleMessage(const EnhancedMessage& msg) override {
        if (msg.idInt == expectedId_) {
            if (msg.result == 0) {
                callback_(true, "");
            } else {
                callback_(false, msg.error);
            }
            return true;  // 消费消息
        }
        return false;
    }

    std::string getName() const override {
        return "RequestTaskResponseHandler_" + std::to_string(expectedId_);
    }

    int getPriority() const override { return 85; }  // 高于普通响应处理器

private:
    int expectedId_;
    std::function<void(bool, const std::string&)> callback_;
};

struct json_object* JsonProtocol::createSetModeRequest(TestMode mode) {
    struct json_object* jsonObj = json_object_new_object();
    if (!jsonObj) {
        return nullptr;
    }
    
    const char* modeStr;
    switch (mode) {
        case TestMode::NORMAL:
            modeStr = "normal";
            break;
        case TestMode::MOCK:
            modeStr = "mock";
            break;
        case TestMode::COLLECT:
            modeStr = "collect";
            break;
        case TestMode::DEBUG:
            modeStr = "debug";
            break;
        default:
            modeStr = "normal";
            break;
    }
    
    json_object_object_add(jsonObj, "mode", json_object_new_string(modeStr));
    
    return jsonObj;
}

struct json_object* JsonProtocol::createActionRequest(const std::string& action) {
    if (action.empty()) {
        return nullptr;
    }
    
    struct json_object* jsonObj = json_object_new_object();
    if (!jsonObj) {
        return nullptr;
    }
    
    json_object_object_add(jsonObj, "action", json_object_new_string(action.c_str()));
    
    return jsonObj;
}

struct json_object* JsonProtocol::createAspectsRequest(const std::vector<std::string>& aspects) {
    if (aspects.empty()) {
        return nullptr;
    }
    
    struct json_object* jsonObj = json_object_new_object();
    if (!jsonObj) {
        return nullptr;
    }
    
    struct json_object* aspectsArray = json_object_new_array();
    if (!aspectsArray) {
        json_object_put(jsonObj);
        return nullptr;
    }
    
    for (const auto& aspect : aspects) {
        json_object_array_add(aspectsArray, json_object_new_string(aspect.c_str()));
    }
    
    json_object_object_add(jsonObj, "aspects", aspectsArray);    
    
    return jsonObj;
}

struct json_object* JsonProtocol::createCallRequest(const std::string& func, struct json_object* args) {
    if (func.empty()) {
        return nullptr;
    }
    
    struct json_object* jsonObj = json_object_new_object();
    if (!jsonObj) {
        return nullptr;
    }
    
    struct json_object* call = json_object_new_object();
    if (!call) {
        json_object_put(jsonObj);
        return nullptr;
    }
    
    json_object_object_add(call, "func", json_object_new_string(func.c_str()));
    
    if (args) {
        json_object_object_add(call, "args", json_object_get(args));
    } else {
        json_object_object_add(call, "args", json_object_new_array());
    }
    
    json_object_object_add(jsonObj, "call", call);
    
    return jsonObj;
}

struct json_object* JsonProtocol::createDebugRequest(const std::string& action, const std::string& addr,
                                                  int bytes, const std::string& data) {
    if (action.empty() || addr.empty()) {
        return nullptr;
    }
    
    struct json_object* jsonObj = json_object_new_object();
    if (!jsonObj) {
        return nullptr;
    }
    
    struct json_object* debug = json_object_new_object();
    if (!debug) {
        json_object_put(jsonObj);
        return nullptr;
    }
    
    json_object_object_add(debug, "action", json_object_new_string(action.c_str()));
    json_object_object_add(debug, "addr", json_object_new_string(addr.c_str()));
    json_object_object_add(debug, "bytes", json_object_new_int(bytes));
    
    if (!data.empty() && action == "write") {
        json_object_object_add(debug, "data", json_object_new_string(data.c_str()));
    }
    
    json_object_object_add(jsonObj, "debug", debug);
    
    return jsonObj;
}

struct json_object* JsonProtocol::createGetPtrRequest(const std::string& symbol) {
    if (symbol.empty()) {
        return nullptr;
    }
    
    struct json_object* jsonObj = json_object_new_object();
    if (!jsonObj) {
        return nullptr;
    }
    
    struct json_object* getPtr = json_object_new_object();
    if (!getPtr) {
        json_object_put(jsonObj);
        return nullptr;
    }
    
    json_object_object_add(getPtr, "symbol", json_object_new_string(symbol.c_str()));
    
    json_object_object_add(jsonObj, "get_ptr", getPtr);
    
    return jsonObj;
}

struct json_object* JsonProtocol::createResponse(int result, const std::string& error,
                                              struct json_object* data) {
    struct json_object* jsonObj = json_object_new_object();
    if (!jsonObj) {
        return nullptr;
    }
    
    json_object_object_add(jsonObj, "result", json_object_new_int(result));
    
    if (!error.empty()) {
        json_object_object_add(jsonObj, "error", json_object_new_string(error.c_str()));
    }
    
    if (data) {
        json_object_object_add(jsonObj, "data", json_object_get(data));
    } else {
        json_object_object_add(jsonObj, "data", json_object_new_array());
    }
    
    return jsonObj;
}

struct json_object* JsonProtocol::createNotify(const std::string& type, const std::string& data) {
    if (type.empty()) {
        return nullptr;
    }
    
    struct json_object* jsonObj = json_object_new_object();
    if (!jsonObj) {
        return nullptr;
    }
    
    json_object_object_add(jsonObj, "type", json_object_new_string(type.c_str()));
    
    if (!data.empty()) {
        json_object_object_add(jsonObj, "data", json_object_new_string(data.c_str()));
    }
    
    return jsonObj;
}

bool JsonProtocol::parseRequest(struct json_object* jsonObj, TestMode& mode, std::string& action) {
    if (!jsonObj) {
        return false;
    }
    
    // 解析模式
    struct json_object* modeObj;
    if (json_object_object_get_ex(jsonObj, "mode", &modeObj)) {
        const char* modeStr = json_object_get_string(modeObj);
        if (modeStr) {
            if (strcmp(modeStr, "normal") == 0) {
                mode = TestMode::NORMAL;
            } else if (strcmp(modeStr, "mock") == 0) {
                mode = TestMode::MOCK;
            } else if (strcmp(modeStr, "collect") == 0) {
                mode = TestMode::COLLECT;
            } else if (strcmp(modeStr, "debug") == 0) {
                mode = TestMode::DEBUG;
            }
        }
    }
    
    // 解析操作
    struct json_object* actionObj;
    if (json_object_object_get_ex(jsonObj, "action", &actionObj)) {
        const char* actionStr = json_object_get_string(actionObj);
        if (actionStr) {
            action = actionStr;
        }
    }
    
    return true;
}

bool JsonProtocol::parseResponse(struct json_object* jsonObj, int& result, std::string& error,
                               struct json_object** data) {
    if (!jsonObj || !data) {
        return false;
    }
    
    // 解析结果
    struct json_object* resultObj;
    if (json_object_object_get_ex(jsonObj, "result", &resultObj)) {
        result = json_object_get_int(resultObj);
    } else {
        return false;
    }
    
    // 解析错误
    struct json_object* errorObj;
    if (json_object_object_get_ex(jsonObj, "error", &errorObj)) {
        const char* errorStr = json_object_get_string(errorObj);
        if (errorStr) {
            error = errorStr;
        }
    }
    
    // 解析数据
    struct json_object* dataObj;
    if (json_object_object_get_ex(jsonObj, "data", &dataObj)) {
        *data = json_object_get(dataObj);
    } else {
        *data = nullptr;
    }
    
    return true;
}

bool JsonProtocol::parseNotify(struct json_object* jsonObj, std::string& type, std::string& data) {
    if (!jsonObj) {
        return false;
    }
    
    // 解析类型
    struct json_object* typeObj;
    if (json_object_object_get_ex(jsonObj, "type", &typeObj)) {
        const char* typeStr = json_object_get_string(typeObj);
        if (typeStr) {
            type = typeStr;
        } else {
            return false;
        }
    } else {
        return false;
    }
    
    // 解析数据
    struct json_object* dataObj;
    if (json_object_object_get_ex(jsonObj, "data", &dataObj)) {
        const char* dataStr = json_object_get_string(dataObj);
        if (dataStr) {
            data = dataStr;
        }
    }
    
    return true;
}

struct json_object* JsonProtocol::createTestControlRequest(const std::string& command,
                                                        const std::string& testId,
                                                        struct json_object* params) {
    if (command.empty()) {
        return nullptr;
    }
    
    struct json_object* jsonObj = json_object_new_object();
    if (!jsonObj) {
        return nullptr;
    }
    
    json_object_object_add(jsonObj, "command", json_object_new_string(command.c_str()));
    
    if (!testId.empty()) {
        json_object_object_add(jsonObj, "test_id", json_object_new_string(testId.c_str()));
    }
    
    if (params) {
        json_object_object_add(jsonObj, "params", json_object_get(params));
    }
    
    return jsonObj;
}

struct json_object* JsonProtocol::createTestControlResponse(const std::string& status,
                                                         const std::string& testId,
                                                         struct json_object* data,
                                                         const std::string& error) {
    if (status.empty()) {
        return nullptr;
    }
    
    struct json_object* jsonObj = json_object_new_object();
    if (!jsonObj) {
        return nullptr;
    }
    
    json_object_object_add(jsonObj, "status", json_object_new_string(status.c_str()));
    
    if (!testId.empty()) {
        json_object_object_add(jsonObj, "test_id", json_object_new_string(testId.c_str()));
    }
    
    if (data) {
        json_object_object_add(jsonObj, "data", json_object_get(data));
    }
    
    if (!error.empty() && status == "ERROR") {
        json_object_object_add(jsonObj, "error", json_object_new_string(error.c_str()));
    }
    
    return jsonObj;
}

bool JsonProtocol::parseTestControlRequest(struct json_object* jsonObj, std::string& command,
                                         std::string& testId, struct json_object** params) {
    if (!jsonObj || !params) {
        return false;
    }
    
    // 解析命令
    struct json_object* commandObj;
    if (json_object_object_get_ex(jsonObj, "command", &commandObj)) {
        const char* commandStr = json_object_get_string(commandObj);
        if (commandStr) {
            command = commandStr;
        } else {
            return false;
        }
    } else {
        return false;
    }
    
    // 解析测试ID
    struct json_object* testIdObj;
    if (json_object_object_get_ex(jsonObj, "test_id", &testIdObj)) {
        const char* testIdStr = json_object_get_string(testIdObj);
        if (testIdStr) {
            testId = testIdStr;
        }
    }
    
    // 解析参数
    struct json_object* paramsObj;
    if (json_object_object_get_ex(jsonObj, "params", &paramsObj)) {
        *params = json_object_get(paramsObj);
    } else {
        *params = nullptr;
    }
    
    return true;
}

bool JsonProtocol::parseTestControlResponse(struct json_object* jsonObj, std::string& status,
                                          std::string& testId, struct json_object** data,
                                          std::string& error) {
    if (!jsonObj || !data) {
        return false;
    }
    
    // 解析状态
    struct json_object* statusObj;
    if (json_object_object_get_ex(jsonObj, "status", &statusObj)) {
        const char* statusStr = json_object_get_string(statusObj);
        if (statusStr) {
            status = statusStr;
        } else {
            return false;
        }
    } else {
        return false;
    }
    
    // 解析测试ID
    struct json_object* testIdObj;
    if (json_object_object_get_ex(jsonObj, "test_id", &testIdObj)) {
        const char* testIdStr = json_object_get_string(testIdObj);
        if (testIdStr) {
            testId = testIdStr;
        }
    }
    
    // 解析数据
    struct json_object* dataObj;
    if (json_object_object_get_ex(jsonObj, "data", &dataObj)) {
        *data = json_object_get(dataObj);
    } else {
        *data = nullptr;
    }
    
    // 解析错误
    struct json_object* errorObj;
    if (json_object_object_get_ex(jsonObj, "error", &errorObj)) {
        const char* errorStr = json_object_get_string(errorObj);
        if (errorStr) {
            error = errorStr;
        }
    }
    
    return true;
}

bool JsonProtocol::parseMessage(struct json_object* jsonObj, EnhancedMessage& msg, std::shared_ptr<ClientConnection> client) {
    if (!jsonObj) {
        return false;
    }

    // 解析类型
    struct json_object* typeObj;
    if (json_object_object_get_ex(jsonObj, "type", &typeObj)) {
        const char* typeStr = json_object_get_string(typeObj);
        if (typeStr) {
            msg.type = typeStr;
        } else {
            return false;
        }
    } else {
        return false;
    }

    // 解析ID
    struct json_object* idObj;
    if (json_object_object_get_ex(jsonObj, "id", &idObj)) {
        const char* idStr = json_object_get_string(idObj);
        if (idStr) {
            msg.id = idStr;
        }
        msg.idInt = json_object_get_int(idObj);
    }

    // 解析结果
    struct json_object* resultObj;
    if (json_object_object_get_ex(jsonObj, "result", &resultObj)) {
        msg.result = json_object_get_int(resultObj);
    }

    // 解析错误
    struct json_object* errorObj;
    if (json_object_object_get_ex(jsonObj, "error", &errorObj)) {
        const char* errorStr = json_object_get_string(errorObj);
        if (errorStr) {
            msg.error = errorStr;
        }
    }

    // 解析数据
    struct json_object* dataObj;
    if (json_object_object_get_ex(jsonObj, "data", &dataObj)) {
        msg.setData(json_object_get(dataObj));
    }

    // 解析日志类型（设置为subType）
    if (msg.type == "log") {
        struct json_object* logTypeObj;
        if (json_object_object_get_ex(jsonObj, "log_type", &logTypeObj)) {
            const char* logTypeStr = json_object_get_string(logTypeObj);
            if (logTypeStr) {
                msg.subType = logTypeStr;
            }
        }
    }

    // 设置消息元数据
    msg.source = MessageSource::CLIENT;
    if (client) {
        msg.clientConnection = client;
        msg.sourceId = client->getProgramName() + ":" + std::to_string(client->getPid());
        msg.addTag("process:" + client->getProgramName());
        msg.addTag("pid:" + std::to_string(client->getPid()));
    }

    // 根据消息类型设置优先级和标签
    if (msg.type == "init") {
        msg.priority = MessagePriority::HIGH;
        msg.addTag("lifecycle");
    } else if (msg.type == "log") {
        msg.priority = MessagePriority::LOW;
        msg.addTag("logging");
    } else if (msg.type == "error") {
        msg.priority = MessagePriority::CRITICAL;
        msg.addTag("error");
    } else {
        msg.priority = MessagePriority::NORMAL;
    }

    return true;
}

void JsonProtocol::processMessage(struct json_object* jsonObj, const std::string& clientUUID) {
    if (!jsonObj) {
        return;
    }

    // 根据UUID获取客户端
    auto client = Context::getInstance().getClientByUUID(clientUUID);
    if (!client) {
        return;
    }

    processMessage(jsonObj, client);
}

void JsonProtocol::processMessage(struct json_object* jsonObj, std::shared_ptr<ClientConnection> client) {
    if (!jsonObj || !client) {
        return;
    }

    // 直接解析为增强消息
    EnhancedMessage msg;
    if (!parseMessage(jsonObj, msg, client)) {
        return;
    }

    // 设置客户端UUID
    msg.clientUUID = client->getUUID();

    // 发布到消息系统
    MessageSystem::getInstance().publish(msg);
}

RequestTask::RequestTask(const std::string& name, const std::string& description, std::weak_ptr<ClientConnection> conn, struct json_object* request_obj, int timeout_ms, Private)
    : Task(name, description, Private()), conn(conn), request_obj(request_obj), responseReceived_(false), responseSuccess_(false) {
    if (request_obj) {
        json_object_get(request_obj);
    }
    if (timeout_ms > 0) {
        struct timespec now;
        clock_gettime(CLOCK_MONOTONIC, &now);
        end_time_s = now.tv_sec;
        end_time_ms = now.tv_nsec / 1000000;
        end_time_ms += timeout_ms;
        end_time_s += end_time_ms / 1000;
        end_time_ms %= 1000;
    }else{
        end_time_s = -1;
    }
    
}

int RequestTask::generateId() {
    static int id = 0;
    return ++id;
}

RequestTask::~RequestTask() {
    // 取消消息订阅
    if (!responseSubscriptionId_.empty()) {
        MessageSystem::getInstance().getRouter().unsubscribe(responseSubscriptionId_);
    }

    if (request_obj) {
        json_object_put(request_obj);
    }
}

int RequestTask::executeCustom() {
    if (state == State::INIT) {
        auto client = conn.lock();
        if (!client) {
            std::cerr << "Client disconnected" << std::endl;
            return -ECONNRESET;
        }

        // 生成请求ID
        id = generateId();
        json_object_object_add(request_obj, "id", json_object_new_int(id));

        // 设置响应处理器
        MessageFilter responseFilter;
        responseFilter.byCustom([this](const EnhancedMessage& msg) {
            return msg.idInt == this->id;
        });

        auto handler = std::make_shared<RequestTaskResponseHandler>(
            id,
            [this](bool success, const std::string& error) {
                responseReceived_ = true;
                responseSuccess_ = success;
                if (success) {
                    setState(State::FINISHED);
                } else {
                    setState(State::ERROR);
                    setResultString(error);
                }
            }
        );

        responseSubscriptionId_ = MessageSystem::getInstance().subscribe(
            responseFilter, handler, 85, true  // 高优先级，消费消息
        );

        // 发送请求
        int ret = Socket::sendJson(client, request_obj);
        if (ret < 0) {
            return ret;
        }

        setState(State::RUNNING);
        return -EAGAIN;
    }

    if (state == State::RUNNING) {
        // 检查是否收到响应
        if (responseReceived_) {
            return 0;  // 任务完成
        }

        // 检查超时
        if (end_time_s > 0) {
            struct timespec now;
            clock_gettime(CLOCK_MONOTONIC, &now);
            if (now.tv_sec > end_time_s || (now.tv_sec == end_time_s && now.tv_nsec / 1000000 > end_time_ms)) {
                std::cerr << "Request timed out" << std::endl;
                setState(State::ERROR);
                setResultString("Request timeout");
                return -ETIMEDOUT;
            }
        }

        return -EAGAIN;  // 继续等待响应
    }

    return 0;
}



} // namespace testd
