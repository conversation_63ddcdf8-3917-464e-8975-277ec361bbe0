#include "message_system.hpp"
#include <algorithm>
#include <chrono>
#include <iostream>
#include <mutex>

namespace testd {

// EnhancedMessage 实现
EnhancedMessage::EnhancedMessage()
    : idInt(0), priority(MessagePriority::NORMAL), source(MessageSource::CLIENT),
      timestamp(0), result(0), data(nullptr) {
    auto now = std::chrono::system_clock::now();
    timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
}

EnhancedMessage::~EnhancedMessage() {
    if (data) {
        json_object_put(data);
    }
}

EnhancedMessage::EnhancedMessage(const EnhancedMessage& other)
    : id(other.id), idInt(other.idInt), type(other.type), subType(other.subType),
      priority(other.priority), source(other.source), sourceId(other.sourceId),
      timestamp(other.timestamp), clientConnection(other.clientConnection),
      clientUUID(other.clientUUID), result(other.result), error(other.error),
      tags(other.tags) {
    if (other.data) {
        data = json_object_get(other.data);
    } else {
        data = nullptr;
    }
}

EnhancedMessage& EnhancedMessage::operator=(const EnhancedMessage& other) {
    if (this != &other) {
        if (data) {
            json_object_put(data);
        }
        
        id = other.id;
        idInt = other.idInt;
        type = other.type;
        subType = other.subType;
        priority = other.priority;
        source = other.source;
        sourceId = other.sourceId;
        timestamp = other.timestamp;
        clientConnection = other.clientConnection;
        clientUUID = other.clientUUID;
        result = other.result;
        error = other.error;
        tags = other.tags;
        
        if (other.data) {
            data = json_object_get(other.data);
        } else {
            data = nullptr;
        }
    }
    return *this;
}



void EnhancedMessage::setData(struct json_object* d) {
    if (data) {
        json_object_put(data);
    }
    data = d;
}

void EnhancedMessage::addTag(const std::string& tag) {
    if (std::find(tags.begin(), tags.end(), tag) == tags.end()) {
        tags.push_back(tag);
    }
}

bool EnhancedMessage::hasTag(const std::string& tag) const {
    return std::find(tags.begin(), tags.end(), tag) != tags.end();
}

// MessageFilter 实现
MessageFilter& MessageFilter::byType(const std::string& type) {
    types_.insert(type);
    return *this;
}

MessageFilter& MessageFilter::bySubType(const std::string& subType) {
    subTypes_.insert(subType);
    return *this;
}

MessageFilter& MessageFilter::bySource(MessageSource source) {
    sources_.insert(source);
    return *this;
}

MessageFilter& MessageFilter::bySourceId(const std::string& sourceId) {
    sourceIds_.insert(sourceId);
    return *this;
}

MessageFilter& MessageFilter::byPriority(MessagePriority minPriority) {
    minPriority_ = minPriority;
    return *this;
}

MessageFilter& MessageFilter::byTag(const std::string& tag) {
    tags_.insert(tag);
    return *this;
}

MessageFilter& MessageFilter::byClientConnection(std::shared_ptr<ClientConnection> client) {
    clientConnections_.push_back(client);
    return *this;
}

MessageFilter& MessageFilter::byClientUUID(const std::string& clientUUID) {
    clientUUIDs_.insert(clientUUID);
    return *this;
}

MessageFilter& MessageFilter::byCustom(std::function<bool(const EnhancedMessage&)> predicate) {
    customPredicates_.push_back(predicate);
    return *this;
}

bool MessageFilter::matches(const EnhancedMessage& msg) const {
    // 检查类型
    if (!types_.empty() && types_.find(msg.type) == types_.end()) {
        return false;
    }
    
    // 检查子类型
    if (!subTypes_.empty() && subTypes_.find(msg.subType) == subTypes_.end()) {
        return false;
    }
    
    // 检查来源
    if (!sources_.empty() && sources_.find(msg.source) == sources_.end()) {
        return false;
    }
    
    // 检查来源ID
    if (!sourceIds_.empty() && sourceIds_.find(msg.sourceId) == sourceIds_.end()) {
        return false;
    }
    
    // 检查优先级
    if (msg.priority < minPriority_) {
        return false;
    }
    
    // 检查标签
    if (!tags_.empty()) {
        bool hasMatchingTag = false;
        for (const auto& tag : tags_) {
            if (msg.hasTag(tag)) {
                hasMatchingTag = true;
                break;
            }
        }
        if (!hasMatchingTag) {
            return false;
        }
    }
    
    // 检查客户端连接
    if (!clientConnections_.empty()) {
        bool hasMatchingConnection = false;
        for (const auto& weakClient : clientConnections_) {
            auto client = weakClient.lock();
            if (client && client == msg.clientConnection.lock()) {
                hasMatchingConnection = true;
                break;
            }
        }
        if (!hasMatchingConnection) {
            return false;
        }
    }

    // 检查客户端UUID
    if (!clientUUIDs_.empty() && clientUUIDs_.find(msg.clientUUID) == clientUUIDs_.end()) {
        return false;
    }

    // 检查自定义谓词
    for (const auto& predicate : customPredicates_) {
        if (!predicate(msg)) {
            return false;
        }
    }

    return true;
}

// MessageSubscription 实现
MessageSubscription::MessageSubscription(const std::string& id, 
                                       const MessageFilter& filter,
                                       std::shared_ptr<MessageHandler> handler,
                                       int priority,
                                       bool consumeMessage)
    : id(id), filter(filter), handler(handler), priority(priority), consumeMessage(consumeMessage) {
}

// MessageRouter 实现
std::string MessageRouter::subscribe(const MessageSubscription& subscription) {
    std::lock_guard<std::mutex> lock(mutex_);
    subscriptions_.push_back(subscription);
    sortSubscriptions();
    return subscription.id;
}

void MessageRouter::unsubscribe(const std::string& subscriptionId) {
    std::lock_guard<std::mutex> lock(mutex_);

    // 使用迭代器方式安全删除
    for (auto it = subscriptions_.begin(); it != subscriptions_.end(); ++it) {
        if (it->id == subscriptionId) {
            subscriptions_.erase(it);
            break;  // 找到并删除后立即退出
        }
    }
}

void MessageRouter::publish(const EnhancedMessage& msg) {
    std::lock_guard<std::mutex> lock(mutex_);
    pendingMessages_.push_back(msg);
}

size_t MessageRouter::getPendingMessageCount() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return pendingMessages_.size();
}

void MessageRouter::processMessages() {
    std::vector<EnhancedMessage> messages;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        messages.swap(pendingMessages_);
    }

    for (const auto& msg : messages) {
        bool consumed = false;

        // 按优先级处理订阅
        for (const auto& subscription : subscriptions_) {
            if (subscription.filter.matches(msg)) {
                try {
                    bool handled = subscription.handler->handleMessage(msg);
                    if (handled && subscription.consumeMessage) {
                        consumed = true;
                        break;
                    }
                } catch (const std::exception& e) {
                    std::cerr << "Error in message handler " << subscription.handler->getName()
                              << ": " << e.what() << std::endl;
                }
            }
        }

        if (!consumed) {
            // 如果消息没有被消费，可以记录日志或进行其他处理
            // 这里暂时不做处理，避免消息丢失
        }
    }
}

void MessageRouter::clearSubscriptions() {
    std::lock_guard<std::mutex> lock(mutex_);
    subscriptions_.clear();
}

void MessageRouter::sortSubscriptions() {
    std::sort(subscriptions_.begin(), subscriptions_.end(),
              [](const MessageSubscription& a, const MessageSubscription& b) {
                  return a.priority > b.priority;  // 高优先级在前
              });
}

// MessageSystem 实现
MessageSystem& MessageSystem::getInstance() {
    static MessageSystem instance;
    return instance;
}

void MessageSystem::publish(const EnhancedMessage& msg) {
    router_.publish(msg);
}

std::string MessageSystem::subscribe(const MessageFilter& filter,
                                   std::shared_ptr<MessageHandler> handler,
                                   int priority,
                                   bool consumeMessage) {
    static int subscriptionCounter = 0;
    std::string id = "sub_" + std::to_string(++subscriptionCounter);
    MessageSubscription subscription(id, filter, handler, priority, consumeMessage);
    return router_.subscribe(subscription);
}

void MessageSystem::processMessages() {
    router_.processMessages();
}

} // namespace testd
