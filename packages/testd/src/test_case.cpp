/**
 * @file test_case.cpp
 * @brief 测试用例管理模块实现 - C++11版本
 */

#include "test_case.hpp"
#include "context.hpp"
#include "json_protocol.hpp"
#include "message_system.hpp"
#include "message_handlers.hpp"
#include <iostream>
#include <fstream>
#include <cstring>
#include <algorithm>
#include <json-c/json_object.h>
#include <memory>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <uuid/uuid.h>
#include <sys/wait.h>
#include <spawn.h>
#include "socket.hpp"

namespace testd {

/**
 * @brief 测试任务专用的Init消息处理器
 */
class TestTaskInitHandler : public MessageHandler {
public:
    TestTaskInitHandler(const std::vector<std::string>& expectedProcesses,
                       std::function<void(std::shared_ptr<ClientConnection>)> onProcessAttached)
        : expectedProcesses_(expectedProcesses), onProcessAttached_(onProcessAttached) {}

    bool handleMessage(const EnhancedMessage& msg) override {
        if (msg.type != "init") {
            return false;
        }

        auto client = msg.clientConnection.lock();
        if (!client) {
            return false;
        }

        // 处理初始化消息，设置客户端信息
        struct json_object* dataObj = msg.getData();
        if (dataObj) {
            struct json_object* pidObj;
            if (json_object_object_get_ex(dataObj, "pid", &pidObj)) {
                int pid = json_object_get_int(pidObj);
                client->setPid(pid);
            }

            struct json_object* programObj;
            if (json_object_object_get_ex(dataObj, "program", &programObj)) {
                const char* program = json_object_get_string(programObj);
                if (program) {
                    client->setProgramName(program);
                }
            }
        }

        // 检查是否为期望的进程
        bool isExpected = false;
        for (const auto& expectedProcess : expectedProcesses_) {
            if (client->getProgramName() == expectedProcess) {
                isExpected = true;
                break;
            }
        }

        if (isExpected) {
            if (Context::getInstance().getConfig().isVerbose()) {
                std::cout << "Expected test process attached: "
                          << client->getProgramName() << " (PID: " << client->getPid() << ")" << std::endl;
            }

            // 通知测试任务
            onProcessAttached_(client);
            return true;  // 消费消息
        }

        return false;  // 不是期望的进程，不处理
    }

    std::string getName() const override {
        return "TestTaskInitHandler";
    }

    int getPriority() const override { return 150; }  // 最高优先级

private:
    std::vector<std::string> expectedProcesses_;
    std::function<void(std::shared_ptr<ClientConnection>)> onProcessAttached_;
};

/**
 * @brief 测试用例专用的日志验证处理器
 */
class TestCaseLogVerificationHandler : public MessageHandler {
public:
    TestCaseLogVerificationHandler(const std::string& expectedLogType,
                                 const std::string& expectedMessage,
                                 std::function<void(bool)> callback)
        : expectedLogType_(expectedLogType), expectedMessage_(expectedMessage), callback_(callback) {}

    bool handleMessage(const EnhancedMessage& msg) override {
        if (msg.type == "log" && msg.subType == expectedLogType_) {
            if (msg.getData()) {
                std::string msgStr = json_object_get_string(msg.getData());
                if (msgStr.find(expectedMessage_) != std::string::npos) {
                    std::cout << "Log verification passed: " << expectedMessage_ << std::endl;
                    callback_(true);
                    return true;  // 消费消息
                } else {
                    std::cerr << "Verify failed: expected log " << expectedMessage_
                              << ", got " << msgStr << std::endl;
                    callback_(false);
                    return true;  // 消费消息
                }
            }
        }
        return false;
    }

    std::string getName() const override {
        return "TestCaseLogVerificationHandler_" + expectedLogType_;
    }

    int getPriority() const override { return 60; }

private:
    std::string expectedLogType_;
    std::string expectedMessage_;
    std::function<void(bool)> callback_;
};

// TestCase类实现
TestCase::TestCase(const std::string& name, const std::string& description, Private)
    : Task(name, description, Private()), setup_obj(nullptr), cases_obj(nullptr), cleanup_obj(nullptr),
      targetPid(0), caseState(0) ,waitTimerFd(-1) {
}

TestCase::~TestCase() {
    if (setup_obj) {
        json_object_put(setup_obj);
    }
    
    if (cases_obj) {
        json_object_put(cases_obj);
    }
    
    if (cleanup_obj) {
        json_object_put(cleanup_obj);
    }
}

std::shared_ptr<TestCase> TestCase::loadFromFile(const std::string& filePath) {
    // 读取文件内容
    std::ifstream file(filePath);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filePath << std::endl;
        return nullptr;
    }
    
    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();
    
    // 解析JSON
    struct json_object* jsonObj = json_tokener_parse(content.c_str());
    if (!jsonObj) {
        std::cerr << "Failed to parse JSON: " << filePath << std::endl;
        return nullptr;
    }
    
    // 获取测试用例名称和描述
    struct json_object* nameObj;
    struct json_object* descObj;
    std::string name;
    std::string description;
    
    if (json_object_object_get_ex(jsonObj, "name", &nameObj)) {
        name = json_object_get_string(nameObj);
    } else {
        std::cerr << "Missing 'name' field in test case file" << std::endl;
        json_object_put(jsonObj);
        return nullptr;
    }
    
    if (json_object_object_get_ex(jsonObj, "description", &descObj)) {
        description = json_object_get_string(descObj);
    }
    
    // 创建测试用例对象
    auto testCase = std::make_shared<TestCase>(name, description, Private());
    
    // 获取setup、cases和cleanup字段
    struct json_object* setupObj;
    struct json_object* casesObj;
    struct json_object* cleanupObj;
    
    std::shared_ptr<TestSetupTask> setupTask;
    if (json_object_object_get_ex(jsonObj, "setup", &setupObj)) {
        testCase->setSetup(setupObj);

        // 创建setup任务
        setupTask = TestSetupTask::create("Setup", "", setupObj);
        testCase->addSubTask(setupTask);
    }
    
    std::shared_ptr<TestCaseListTask> casesTask;
    if (json_object_object_get_ex(jsonObj, "cases", &casesObj)) 
    {
        testCase->setCases(casesObj);
        // 创建cases任务
        casesTask = TestCaseListTask::create("Cases", "", casesObj);
        if (setupObj) {
            casesTask->setAfterTask(setupTask);
        }
        testCase->addSubTask(casesTask);
        testCase->casesTask = casesTask;
    } else {
        std::cerr << "Missing 'cases' field in test case file" << std::endl;
        json_object_put(jsonObj);
        return nullptr;
    }
    
    if (json_object_object_get_ex(jsonObj, "cleanup", &cleanupObj)) {
        testCase->setCleanup(cleanupObj);
    }
    
    // 释放JSON对象
    json_object_put(jsonObj);
    
    return testCase;
}

int TestCase::executeCustom() {
    if (state == State::INIT) {
        // 生成测试ID
        testId = generateTestId();
        result = std::make_shared<TestResult>(testId);
        
        return 0;
    }
    if (state == State::RUNNING) {
        if (cleanup_obj) {
            struct json_object* cleanupItem;
            for (size_t i = 0; i < json_object_array_length(cleanup_obj); i++) {
                cleanupItem = json_object_array_get_idx(cleanup_obj, i);
                struct json_object* exitObj;
                if (json_object_object_get_ex(cleanupItem, "exit", &exitObj)) {
                    std::string target = json_object_get_string(exitObj);
                    auto client = Context::getInstance().findClientByProgramName(target);
                    if (!client) {
                        std::cerr << "Client not found: " << target << std::endl;
                        continue;
                    }
                    auto requestTask = RequestTask::create("Exit", "", client, JsonProtocol::createActionRequest("exit"), -1);
                    this->addSubTask(requestTask);
                    requestTask->setAfterTask(casesTask);
                }
                
            }
            json_object_put(cleanup_obj);
            cleanup_obj = nullptr;
            return -EAGAIN;
        }
        result->parseCaseListTask(casesTask.lock());
        return 0;
    }
    return 0;
}

std::string TestCase::generateTestId() {
    uuid_t uuid;
    char uuid_str[37];
    
    uuid_generate(uuid);
    uuid_unparse_lower(uuid, uuid_str);
    
    return std::string(uuid_str);
}

void TestCase::setSetup(struct json_object* s) {
    if (setup_obj) {
        json_object_put(setup_obj);
    }
    setup_obj = s;
    if (setup_obj) {
        json_object_get(setup_obj);
    }
}

void TestCase::setCases(struct json_object* c) {
    if (cases_obj) {
        json_object_put(cases_obj);
    }
    cases_obj = c;
    if (cases_obj) {
        json_object_get(cases_obj);
    }
}

void TestCase::setCleanup(struct json_object* c) {
    if (cleanup_obj) {
        json_object_put(cleanup_obj);
    }
    cleanup_obj = c;
    if (cleanup_obj) {
        json_object_get(cleanup_obj);
    }
}

// TestResult类实现
TestResult::TestResult(const std::string& testId)
    : testId(testId), total(0), passed(0), failed(0), durationMs(0), caseResults(nullptr) {
    caseResults = json_object_new_array();
}

TestResult::~TestResult() {
    if (caseResults) {
        json_object_put(caseResults);
    }
}

void TestResult::addCaseResult(const std::string& caseName, bool passed, const std::string& message) {
    struct json_object* caseResult = json_object_new_object();
    
    json_object_object_add(caseResult, "name", json_object_new_string(caseName.c_str()));
    json_object_object_add(caseResult, "passed", json_object_new_boolean(passed));
    
    if (!message.empty()) {
        json_object_object_add(caseResult, "message", json_object_new_string(message.c_str()));
    }
    
    json_object_array_add(caseResults, caseResult);
    if (passed) {
        this->passed++;
    } else {
        this->failed++;
    }
    this->total++;
}

void TestResult::parseCaseListTask(std::shared_ptr<TestCaseListTask> task) {
    for (auto t : task->getSubTasks()) {
        if (t->getState() == Task::State::FINISHED) {
            addCaseResult(t->getName(), true);
        } else {
            addCaseResult(t->getName(), false, "Task failed");
        }
    }
}

void TestResult::setFinished(int total, int passed, int failed, int durationMs) {
    this->total = total;
    this->passed = passed;
    this->failed = failed;
    this->durationMs = durationMs;
}

bool TestResult::saveToFile(const std::string& filePath) const {
    struct json_object* result = json_object_new_object();
    
    json_object_object_add(result, "test_id", json_object_new_string(testId.c_str()));
    json_object_object_add(result, "total", json_object_new_int(total));
    json_object_object_add(result, "passed", json_object_new_int(passed));
    json_object_object_add(result, "failed", json_object_new_int(failed));
    json_object_object_add(result, "duration_ms", json_object_new_int(durationMs));
    json_object_object_add(result, "case_results", json_object_get(caseResults));
    
    const char* jsonStr = json_object_to_json_string_ext(result, JSON_C_TO_STRING_PRETTY);
    
    std::ofstream file(filePath);
    if (!file.is_open()) {
        std::cerr << "Failed to open file for writing: " << filePath << std::endl;
        json_object_put(result);
        return false;
    }
    
    file << jsonStr;
    file.close();
    
    json_object_put(result);
    return true;
}

TestSetupTask::TestSetupTask(const std::string& name, const std::string& description, struct json_object* setup_obj, Private)
    : Task(name, description, Private()), setup_obj(setup_obj) {
    if (setup_obj) {
        json_object_get(setup_obj);
    }

    // 创建子任务
    int setup_obj_length = json_object_array_length(setup_obj);
    for (int i = 0; i < setup_obj_length; i++) {
        struct json_object* setupStep = json_object_array_get_idx(setup_obj, i);
        struct json_object* actionObj;
        std::weak_ptr<Task> lastTask;
        if (json_object_object_get_ex(setupStep, "action", &actionObj)) {
            std::string action = json_object_get_string(actionObj);
            if (action == "exec") {
                struct json_object* commandObj;
                if (json_object_object_get_ex(setupStep, "command", &commandObj)) {
                    std::vector<std::string> command;
                    int length = json_object_array_length(commandObj);
                    for (int i = 0; i < length; i++) {
                        struct json_object* commandItem = json_object_array_get_idx(commandObj, i);
                        std::string commandItemStr = json_object_get_string(commandItem);
                        command.push_back(commandItemStr);
                    }
                    auto t = TestExecTask::create("Exec", "", command);
                    t->setAfterTask(lastTask);
                    lastTask = addSubTask(t);
                }
            } else if (action == "wait") {
                int waitMs = 0;
                struct json_object* waitObj;
                if (json_object_object_get_ex(setupStep, "wait_ms", &waitObj)) {
                    waitMs = json_object_get_int(waitObj);
                }
                auto t = TestWaitTimeTask::create("Wait", "", waitMs);
                t->setAfterTask(lastTask);
                lastTask = addSubTask(t);
            } else if (action == "wait_attach") {
                int waitMs = 0;
                struct json_object* processesObj;
                if (json_object_object_get_ex(setupStep, "processes", &processesObj)) {
                    int length = json_object_array_length(processesObj);
                    std::vector<std::string> processes;
                    for (int i = 0; i < length; i++) {
                        struct json_object* processObj = json_object_array_get_idx(processesObj, i);
                        std::string process = json_object_get_string(processObj);
                        processes.push_back(process);
                    }
                    struct json_object* waitObj;
                    if (json_object_object_get_ex(setupStep, "wait_ms", &waitObj)) {
                        waitMs = json_object_get_int(waitObj);
                    }
                    auto t = TestWaitAttachTask::create("WaitAttach", "", processes, waitMs);
                    t->setAfterTask(lastTask);
                    lastTask = addSubTask(t);
                }
            }
        }
    }
}

TestSetupTask::~TestSetupTask() {
    if (setup_obj) {
        json_object_put(setup_obj);
    }
}

TestWaitAttachTask::TestWaitAttachTask(const std::string& name, const std::string& description, std::vector<std::string>& processes, int timeout_ms, Private)
    : Task(name, description, Private()), processes(processes) {
    if (timeout_ms > 0) {
        struct timespec now;
        clock_gettime(CLOCK_MONOTONIC, &now);
        end_time_s = now.tv_sec;
        end_time_ms = now.tv_nsec / 1000000;
        end_time_ms += timeout_ms;
        end_time_s += end_time_ms / 1000;
        end_time_ms %= 1000;
    }else{
        end_time_s = -1;
    }
}

TestWaitAttachTask::~TestWaitAttachTask() {
    unregisterMessageHandlers();
}

int TestWaitAttachTask::executeCustom() {
    if (state == State::INIT) {
        // 注册消息处理器来监听期望进程的init消息
        registerInitMessageHandlers();
        setState(State::RUNNING);
        return -EAGAIN;
    }

    if (state == State::RUNNING) {
        // 检查是否所有期望的进程都已连接
        std::vector<std::string> remainingProcesses = processes;

        for (const auto& client : expectedClients_) {
            auto it = std::find(remainingProcesses.begin(), remainingProcesses.end(), client->getProgramName());
            if (it != remainingProcesses.end()) {
                remainingProcesses.erase(it);
            }
        }

        if (remainingProcesses.empty()) {
            std::cout << "All processes attached" << std::endl;
            setState(State::FINISHED);
            return 0;
        }

        // 检查超时
        if (end_time_s > 0) {
            struct timespec now;
            clock_gettime(CLOCK_MONOTONIC, &now);
            if (now.tv_sec > end_time_s || (now.tv_sec == end_time_s && now.tv_nsec / 1000000 > end_time_ms)) {
                std::cout << "Timeout waiting for processes: ";
                for (const auto& process : remainingProcesses) {
                    std::cout << process << " ";
                }
                std::cout << std::endl;
                setState(State::ERROR);
                return -ETIMEDOUT;
            }
        }

        return -EAGAIN;
    }

    return 0;
}

void TestWaitAttachTask::registerInitMessageHandlers() {
    // 创建处理器，监听期望进程的init消息
    auto handler = std::make_shared<TestTaskInitHandler>(
        processes,
        [this](std::shared_ptr<ClientConnection> client) {
            // 当期望的进程连接时，添加到已连接列表
            expectedClients_.push_back(client);
        }
    );

    // 注册消息处理器，使用高优先级
    MessageFilter filter;
    filter.byType("init");

    std::string subscriptionId = MessageSystem::getInstance().subscribe(
        filter, handler, 150, true  // 最高优先级，消费消息
    );

    messageSubscriptionIds_.push_back(subscriptionId);
}

void TestWaitAttachTask::unregisterMessageHandlers() {
    for (const auto& subscriptionId : messageSubscriptionIds_) {
        MessageSystem::getInstance().getRouter().unsubscribe(subscriptionId);
    }
    messageSubscriptionIds_.clear();
}

TestWaitTimeTask::TestWaitTimeTask(const std::string& name, const std::string& description, int wait_ms, Private)
    : Task(name, description, Private()) {
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);
    end_time_s = now.tv_sec;
    end_time_ms = now.tv_nsec / 1000000;
    end_time_ms += wait_ms;
    end_time_s += end_time_ms / 1000;
    end_time_ms %= 1000;
}

TestWaitTimeTask::~TestWaitTimeTask() {
}

int TestWaitTimeTask::executeCustom() {
    if (state == State::INIT) {
        return 0;
    }
    if (state == State::RUNNING) {
        struct timespec now;
        clock_gettime(CLOCK_MONOTONIC, &now);
        if (now.tv_sec > end_time_s || (now.tv_sec == end_time_s && now.tv_nsec / 1000000 > end_time_ms)) {
            std::cout << "Time elapsed" << std::endl;
            return 0;
        }
    }
    return -EAGAIN;
}

TestCaseListTask::TestCaseListTask(const std::string& name, const std::string& description, struct json_object* case_obj, Private)
    : SequentialTask(name, description, true, Private()), case_obj(case_obj) {
    if (case_obj) {
        json_object_get(case_obj);
    }
    // 创建子任务

    int casesCount = json_object_array_length(case_obj);
    std::string case_name;
    for (int i = 0; i < casesCount; i++) {
        struct json_object* caseObj = json_object_array_get_idx(case_obj, i);
        struct json_object* NameObj;
        if (json_object_object_get_ex(case_obj, "name", &NameObj)) {
            case_name = json_object_get_string(NameObj);
        }else{
            case_name = "Case " + std::to_string(i);
        }
        auto t = TestCaseTask::create(case_name, "", caseObj);
        addSubTask(t);
    }
}

TestCaseListTask::~TestCaseListTask() {
    if (case_obj) {
        json_object_put(case_obj);
    }
}

int TestCaseListTask::executeCustom() {
    //TODO
    return 0;
}

TestCaseTask::TestCaseTask(const std::string& name, const std::string& description, struct json_object* case_obj, Private)
    : SequentialTask(name, description, false, Private()), case_obj(case_obj) {
    if (case_obj) {
        json_object_get(case_obj);
    }

    struct json_object* targetsObj;
    if (json_object_object_get_ex(case_obj, "targets", &targetsObj)) {
        int targetCount = json_object_array_length(targetsObj);
        for (int i = 0; i < targetCount; i++) {
            struct json_object* targetObj = json_object_array_get_idx(targetsObj, i);
            auto t = TestCaseRunTask::create("Run", "", targetObj);
            addSubTask(t);
        }
    }
    struct json_object* verifyObj;
    if (json_object_object_get_ex(case_obj, "verify", &verifyObj)) {
        auto t = TestCaseVerifyTask::create("Verify", "", verifyObj);
        addSubTask(t);
    }

}

TestCaseTask::~TestCaseTask() {
    if (case_obj) {
        json_object_put(case_obj);
    }
}

int TestCaseTask::executeCustom() {
    //TODO
    return 0;
}

TestCaseRunTask::TestCaseRunTask(const std::string& name, const std::string& description, struct json_object* case_obj, Private)
    : Task(name, description, Private()), case_obj(case_obj) {
    if (case_obj) {
        json_object_get(case_obj);
    }
}

TestCaseRunTask::~TestCaseRunTask() {
    if (case_obj) {
        json_object_put(case_obj);
    }
}

int TestCaseRunTask::executeCustom() {
    if (state == State::INIT) {
        // 发送请求前，先查找client，如果没有找到，则返回-EAGAIN
        if (client.expired())
        {
            struct json_object* programObj;
            std::string programName;
            if (json_object_object_get_ex(case_obj, "process", &programObj)) {
                programName = json_object_get_string(programObj);
            }else{
                return -EINVAL;
            }
            client = Context::getInstance().findClientByProgramName(programName);
            if (client.expired()) {
                return -EAGAIN;
            }

            std::shared_ptr<Task> lastTask = nullptr;
            
            // 发送aspects请求
            struct json_object* aspectsObj;
            std::vector<std::string> aspects = {};
            if (json_object_object_get_ex(case_obj, "aspects", &aspectsObj)) {
                int aspectsCount = json_object_array_length(aspectsObj);
                for (int i = 0; i < aspectsCount; i++) {
                    struct json_object* aspectObj = json_object_array_get_idx(aspectsObj, i);
                    std::string aspectName = json_object_get_string(aspectObj);
                    aspects.push_back(aspectName);
                }
                auto setAspectsTask = RequestTask::create("SetAspects", "", client, JsonProtocol::createAspectsRequest( aspects), -1);
                lastTask = setAspectsTask;
                addSubTask(setAspectsTask);
            }


            // 发送mocks请求
            struct json_object* mocksList;
            if (json_object_object_get_ex(case_obj, "mocks", &mocksList)) {

                struct json_object* mocksObj = json_object_new_object();
                json_object_object_add(mocksObj, "mocks", mocksList);
                auto mocksTask = RequestTask::create("Mocks", "", client, mocksObj, -1);
                json_object_put(mocksObj);
                mocksTask->setAfterTask(lastTask);
                addSubTask(mocksTask);
                lastTask = mocksTask;
            }

            // 发送mode请求
            auto modeTask = RequestTask::create("Mode", "", client, JsonProtocol::createSetModeRequest(TestMode::MOCK), -1);
            modeTask->setAfterTask(lastTask);
            addSubTask(modeTask);
            lastTask = modeTask;

            // 发送bkpt请求
            struct json_object* bkptListObj;
            if (json_object_object_get_ex(case_obj, "breakpoints", &bkptListObj)) {
                struct json_object* bkptObj = json_object_new_object();
                json_object_object_add(bkptObj, "breakpoints", bkptListObj);
                auto bkptTask = RequestTask::create("Breakpoints", "", client, bkptObj, -1);
                json_object_put(bkptObj);
                bkptTask->setAfterTask(lastTask);
                addSubTask(bkptTask);
                lastTask = bkptTask;
            }

            // 发送run请求
            auto actionTask = RequestTask::create("Action", "", client, JsonProtocol::createActionRequest("run"), -1);
            actionTask->setAfterTask(lastTask);
            addSubTask(actionTask);
            return -EAGAIN;
        }
        return 0;
    }
    if (state == State::RUNNING) {
        // 所有请求已发送
        return 0;
    }
    return 0;
}

TestCaseVerifyTask::TestCaseVerifyTask(const std::string& name, const std::string& description, struct json_object* verify_obj, Private)
    : Task(name, description, Private()), verify_obj(verify_obj), expect_debug_log(false),
      has_command(false), expected_exit_code(0), command_pid(-1), command_started(false),
      command_finished(false), actual_exit_code(-1), logVerificationPassed_(false), logVerificationFailed_(false) {
    if (verify_obj) {
        json_object_get(verify_obj);
    }
}

TestCaseVerifyTask::~TestCaseVerifyTask() {
    // 取消消息订阅
    if (!logSubscriptionId_.empty()) {
        MessageSystem::getInstance().getRouter().unsubscribe(logSubscriptionId_);
    }

    if (verify_obj) {
        json_object_put(verify_obj);
    }
}

int TestCaseVerifyTask::executeCustom() {
    if (state == State::INIT) {
        // 检查是否有 expect_debug_log 配置
        struct json_object* logObj;
        if (json_object_object_get_ex(verify_obj, "expect_debug_log", &logObj)) {
            expect_debug_log = true;
            struct json_object *logTypeObj, *logMessageObj;
            if (json_object_object_get_ex(logObj, "type", &logTypeObj) &&
                json_object_object_get_ex(logObj, "message", &logMessageObj)) {
                log_type = json_object_get_string(logTypeObj);
                log_message = json_object_get_string(logMessageObj);
            }else {
                std::cerr << "Missing 'type' or 'message' field in expect_debug_log" << std::endl;
                return -EINVAL;
            }
        }

        // 检查是否有 command 配置
        struct json_object* commandObj;
        if (json_object_object_get_ex(verify_obj, "command", &commandObj)) {
            has_command = true;
            command = json_object_get_string(commandObj);

            // 检查期望的退出码，默认为0
            struct json_object* exitCodeObj;
            if (json_object_object_get_ex(verify_obj, "expect_exit_code", &exitCodeObj)) {
                expected_exit_code = json_object_get_int(exitCodeObj);
            } else {
                expected_exit_code = 0;  // 默认期望成功退出
            }
        }

        // 如果既没有日志验证也没有命令验证，则报错
        if (!expect_debug_log && !has_command) {
            std::cerr << "No verification method specified (expect_debug_log or command)" << std::endl;
            return -EINVAL;
        }

        // 如果有日志验证，设置消息订阅
        if (expect_debug_log) {
            MessageFilter logFilter;
            logFilter.byType("log").bySubType(log_type);

            auto handler = std::make_shared<TestCaseLogVerificationHandler>(
                log_type,
                log_message,
                [this](bool success) {
                    if (success) {
                        logVerificationPassed_ = true;
                    } else {
                        logVerificationFailed_ = true;
                        setState(State::ERROR);
                    }
                }
            );

            logSubscriptionId_ = MessageSystem::getInstance().subscribe(
                logFilter, handler, 60, true  // 高优先级，消费消息
            );
        }

        return 0;
    }
    if (state == State::RUNNING) {
        // 处理命令验证
        if (has_command) {
            if (!command_started) {
                // 启动命令执行
                std::cout << "Executing verify command: " << command << std::endl;

                command_pid = fork();
                if (command_pid == 0) {
                    // 子进程：执行命令
                    execl("/bin/sh", "sh", "-c", command.c_str(), (char*)nullptr);
                    _exit(127);  // 如果execl失败
                } else if (command_pid > 0) {
                    // 父进程：记录已启动
                    command_started = true;
                    std::cout << "Started verify command (PID: " << command_pid << ")" << std::endl;
                } else {
                    // fork失败
                    std::cerr << "Failed to fork for verify command: " << command << std::endl;
                    state = State::ERROR;
                    return 0;
                }
            } else if (!command_finished) {
                // 检查命令是否完成
                int status;
                pid_t result = waitpid(command_pid, &status, WNOHANG);
                if (result == command_pid) {
                    // 命令已完成
                    command_finished = true;
                    if (WIFEXITED(status)) {
                        actual_exit_code = WEXITSTATUS(status);
                        std::cout << "Verify command exited with code: " << actual_exit_code << std::endl;

                        if (actual_exit_code == expected_exit_code) {
                            std::cout << "Command verification passed (exit code: " << actual_exit_code << ")" << std::endl;
                            // 如果还有日志验证，继续检查；否则完成
                            if (!expect_debug_log) {
                                setState(State::FINISHED);
                                return 0;  // 验证成功
                            }
                        } else {
                            std::cerr << "Command verification failed: expected exit code "
                                     << expected_exit_code << ", got " << actual_exit_code << std::endl;
                            state = State::ERROR;
                            return 0;
                        }
                    } else if (WIFSIGNALED(status)) {
                        int signal = WTERMSIG(status);
                        std::cerr << "Verify command terminated by signal: " << signal << std::endl;
                        state = State::ERROR;
                        return 0;
                    }
                } else if (result == -1) {
                    std::cerr << "Error waiting for verify command" << std::endl;
                    state = State::ERROR;
                    return 0;
                }
                // 命令还在运行，继续等待
            }
        }

        // 处理日志验证（通过消息系统）
        if (expect_debug_log) {
            // 检查日志验证结果
            if (logVerificationFailed_) {
                // 日志验证失败，已在回调中设置错误状态
                return 0;
            }

            if (logVerificationPassed_) {
                // 日志验证成功，检查是否还需要等待命令验证
                if (!has_command || command_finished) {
                    setState(State::FINISHED);
                    return 0;  // 验证成功
                }
            }

            // 继续等待日志验证结果
        }

        // 检查是否所有验证都已完成
        bool commandOk = !has_command || (command_finished && actual_exit_code == expected_exit_code);
        bool logOk = !expect_debug_log || logVerificationPassed_;

        if (commandOk && logOk) {
            setState(State::FINISHED);
            return 0;
        }

        // 检查是否有验证失败
        if ((has_command && command_finished && actual_exit_code != expected_exit_code) ||
            logVerificationFailed_) {
            setState(State::ERROR);
            return 0;
        }

        return -EAGAIN;  // 继续等待
    }
    return 0;
}

TestExecTask::TestExecTask(const std::string& name, const std::string& description, const std::vector<std::string>& command, Private)
    : Task(name, description, Private()), command(command), child_pid(-1) {
}

TestExecTask::~TestExecTask() {
}

int TestExecTask::executeCustom() {
    if (state == State::INIT) {
        // 创建管道用于捕获标准输出和标准错误
        if (pipe(stdout_pipe) == -1 || pipe(stderr_pipe) == -1) {
            std::cerr << "Failed to create pipes for command: " << command.front() << std::endl;
            return -1;
        }

        std::vector<char*> argv;
        for (const auto& arg : command) {
            argv.push_back(const_cast<char*>(arg.c_str()));
        }
        argv.push_back(nullptr);

        posix_spawn_file_actions_t file_actions;
        posix_spawn_file_actions_init(&file_actions);

        // 重定向标准输出和标准错误到管道
        posix_spawn_file_actions_adddup2(&file_actions, stdout_pipe[1], STDOUT_FILENO);
        posix_spawn_file_actions_adddup2(&file_actions, stderr_pipe[1], STDERR_FILENO);
        posix_spawn_file_actions_addclose(&file_actions, stdout_pipe[0]);
        posix_spawn_file_actions_addclose(&file_actions, stderr_pipe[0]);
        posix_spawn_file_actions_addclosefrom_np(&file_actions, 3);
        posix_spawn_file_actions_addclose(&file_actions, 0);

        int status = posix_spawn(&child_pid, command.front().c_str(), &file_actions, NULL, argv.data(), environ);
        posix_spawn_file_actions_destroy(&file_actions);

        if (status != 0) {
            std::cerr << "Failed to spawn process for command: " << command.front() << std::endl;
            close(stdout_pipe[0]);
            close(stdout_pipe[1]);
            close(stderr_pipe[0]);
            close(stderr_pipe[1]);
            return -1;
        }

        // 关闭子进程端的管道
        close(stdout_pipe[1]);
        close(stderr_pipe[1]);
        stdout_pipe[1] = stderr_pipe[1] = -1;

        std::cout << "Started process (PID: " << child_pid << ") for command: " << command.front() << std::endl;
        return 0;
    }

    if (state == State::RUNNING) {
        // 检查子进程是否已经退出
        int status;
        if (waitpid(child_pid, &status, WNOHANG) == child_pid) {
            std::cout << "Process (PID: " << child_pid << ") exited" << std::endl;
            std::cout << "Status: " << status << std::endl;
            state = State::ERROR;
            return -EIO;
        }

        auto allClients = Context::getInstance().getAllClients();
        for (const auto& client : allClients) {
            if (client->getPid() == child_pid) {
                client->setIsChild(true);
                client->setPipeFd(1, stdout_pipe[0]);
                client->setPipeFd(2, stderr_pipe[0]);
                break;
            }
        }
        return 0;
    }

    return 0;
}

// void TestExecTask::printProcessResult() {
//     std::cout << "\n=== Process Result for: " << command << " ===" << std::endl;
//     std::cout << "PID: " << child_pid << std::endl;

//     if (WIFEXITED(exit_status)) {
//         int exit_code = WEXITSTATUS(exit_status);
//         std::cout << "Exit Code: " << exit_code << std::endl;
//         if (exit_code == 0) {
//             std::cout << "Status: SUCCESS" << std::endl;
//         } else {
//             std::cout << "Status: FAILED" << std::endl;
//         }
//     } else if (WIFSIGNALED(exit_status)) {
//         int signal = WTERMSIG(exit_status);
//         std::cout << "Terminated by signal: " << signal << std::endl;
//         std::cout << "Status: TERMINATED" << std::endl;
//     } else {
//         std::cout << "Status: UNKNOWN" << std::endl;
//     }

//     if (!stdout_buffer.empty()) {
//         std::cout << "\n--- Standard Output ---" << std::endl;
//         std::cout << stdout_buffer;
//         if (stdout_buffer.back() != '\n') {
//             std::cout << std::endl;
//         }
//     }

//     if (!stderr_buffer.empty()) {
//         std::cout << "\n--- Standard Error ---" << std::endl;
//         std::cout << stderr_buffer;
//         if (stderr_buffer.back() != '\n') {
//             std::cout << std::endl;
//         }
//     }

//     std::cout << "=== End Process Result ===" << std::endl << std::endl;
// }

} // namespace testd
