# testd 基于任务的消息处理机制

## 概述

本文档描述了testd中新实现的基于任务的消息处理机制，该机制允许测试任务根据自己的需求注册专用的消息处理器，实现更精确和灵活的消息处理控制。

## 设计目标

### 问题背景
1. **时序问题**：在init消息处理之前，`ClientConnection`可能还没有`programName`或`pid`
2. **控制粒度**：应该由具体的测试任务来决定如何处理init消息，而不是全局处理器
3. **过滤精度**：需要支持基于连接ID或其他早期可用信息的过滤

### 解决方案
- 让`TestWaitAttachTask`和`TestExecTask`等测试任务自己注册消息处理器
- 增强消息过滤机制，支持基于客户端连接和索引的过滤
- 实现高优先级的任务专用消息处理器

## 核心组件

### 1. 增强的消息过滤机制

#### 新增的过滤条件
```cpp
class MessageFilter {
public:
    // 新增的过滤方法
    MessageFilter& byClientConnection(std::shared_ptr<ClientConnection> client);
    MessageFilter& byClientIndex(int clientIndex);
    
private:
    // 新增的过滤字段
    std::vector<std::weak_ptr<ClientConnection>> clientConnections_;
    std::unordered_set<int> clientIndices_;
};
```

#### 增强的消息结构
```cpp
struct EnhancedMessage {
    // 新增的连接信息
    std::weak_ptr<ClientConnection> clientConnection;  // 客户端连接
    int clientIndex;       // 客户端索引
};
```

### 2. 任务专用的Init消息处理器

#### TestTaskInitHandler
```cpp
class TestTaskInitHandler : public MessageHandler {
public:
    TestTaskInitHandler(const std::vector<std::string>& expectedProcesses,
                       std::function<void(std::shared_ptr<ClientConnection>)> onProcessAttached);
    
    bool handleMessage(const EnhancedMessage& msg) override;
    int getPriority() const override { return 150; }  // 最高优先级
};
```

**特性**：
- **最高优先级**：确保在其他处理器之前处理init消息
- **进程过滤**：只处理期望的进程的init消息
- **回调机制**：当期望进程连接时通知测试任务
- **消息消费**：消费处理的消息，阻止其他处理器处理

### 3. 测试任务的消息处理能力

#### TestWaitAttachTask增强
```cpp
class TestWaitAttachTask : public Task {
private:
    // 消息处理相关
    std::vector<std::string> messageSubscriptionIds_;
    std::vector<std::shared_ptr<ClientConnection>> expectedClients_;
    
    // 注册init消息处理器
    void registerInitMessageHandlers();
    void unregisterMessageHandlers();
};
```

## 工作流程

### 1. 任务启动阶段
```cpp
int TestWaitAttachTask::executeCustom() {
    if (state == State::INIT) {
        // 注册消息处理器来监听期望进程的init消息
        registerInitMessageHandlers();
        setState(State::RUNNING);
        return -EAGAIN;
    }
    // ...
}
```

### 2. 消息处理器注册
```cpp
void TestWaitAttachTask::registerInitMessageHandlers() {
    // 创建处理器，监听期望进程的init消息
    auto handler = std::make_shared<TestTaskInitHandler>(
        processes,
        [this](std::shared_ptr<ClientConnection> client) {
            // 当期望的进程连接时，添加到已连接列表
            expectedClients_.push_back(client);
        }
    );
    
    // 注册消息处理器，使用高优先级
    MessageFilter filter;
    filter.byType("init");
    
    std::string subscriptionId = MessageSystem::getInstance().subscribe(
        filter, handler, 150, true  // 最高优先级，消费消息
    );
    
    messageSubscriptionIds_.push_back(subscriptionId);
}
```

### 3. 消息处理优先级
1. **TestTaskInitHandler** (优先级150) - 处理待测进程的init消息
2. **InitMessageHandler** (优先级100) - 处理非待测进程的init消息

### 4. 任务状态检查
```cpp
if (state == State::RUNNING) {
    // 检查是否所有期望的进程都已连接
    std::vector<std::string> remainingProcesses = processes;
    
    for (const auto& client : expectedClients_) {
        auto it = std::find(remainingProcesses.begin(), remainingProcesses.end(), 
                           client->getProgramName());
        if (it != remainingProcesses.end()) {
            remainingProcesses.erase(it);
        }
    }
    
    if (remainingProcesses.empty()) {
        std::cout << "All processes attached" << std::endl;
        setState(State::FINISHED);
        return 0;
    }
    // ...
}
```

## 测试验证

### 测试场景
1. **期望进程连接** - 验证TestTaskInitHandler正确处理期望进程的init消息
2. **非期望进程连接** - 验证InitMessageHandler处理非期望进程的init消息
3. **任务状态更新** - 验证任务状态在所有进程连接后正确更新
4. **超时处理** - 验证超时机制正常工作

### 测试结果
```
=== 测试任务消息处理功能测试 ===

--- 启动任务 ---
Task init result: -11
Task state: 1

--- 模拟非期望进程连接 ---
Simulated init message for apache (PID: 1001)
Client not found for init message: 

--- 模拟期望进程连接 ---
Simulated init message for nginx (PID: 1002)
Simulated init message for redis (PID: 1003)

Task after 2 processes: -11
Task state: 1

--- 连接最后一个进程 ---
Simulated init message for mysql (PID: 1004)
All processes attached

Task after all processes: 0
Task state: 2

--- 测试超时情况 ---
Timeout task init result: -11
Timeout waiting for processes: nonexistent 
Timeout task after timeout: -110
Timeout task state: 3
```

## 优势

### 1. 精确控制
- **任务级控制**：每个测试任务可以精确控制自己需要的消息
- **优先级保证**：高优先级确保任务专用处理器优先处理
- **消息消费**：避免消息被多个处理器重复处理

### 2. 时序无关
- **早期过滤**：基于客户端连接和索引进行过滤，不依赖进程名
- **动态注册**：任务启动时动态注册处理器
- **及时响应**：消息到达时立即处理，无需轮询

### 3. 灵活扩展
- **可扩展架构**：其他测试任务可以采用相同模式
- **自定义处理**：每个任务可以定义自己的消息处理逻辑
- **资源管理**：任务结束时自动清理消息订阅

### 4. 性能优化
- **减少轮询**：基于事件驱动，无需定期检查客户端列表
- **精确匹配**：只处理相关消息，减少无效处理
- **内存效率**：使用weak_ptr避免循环引用

## 使用指南

### 为新的测试任务添加消息处理

1. **添加消息处理成员变量**
```cpp
class YourTestTask : public Task {
private:
    std::vector<std::string> messageSubscriptionIds_;
    // 其他相关成员
};
```

2. **实现消息处理器注册**
```cpp
void YourTestTask::registerMessageHandlers() {
    auto handler = std::make_shared<YourCustomHandler>(/* 参数 */);
    
    MessageFilter filter;
    filter.byType("your_message_type");
    
    std::string subscriptionId = MessageSystem::getInstance().subscribe(
        filter, handler, priority, consumeMessage
    );
    
    messageSubscriptionIds_.push_back(subscriptionId);
}
```

3. **在任务执行中注册和清理**
```cpp
int YourTestTask::executeCustom() {
    if (state == State::INIT) {
        registerMessageHandlers();
        setState(State::RUNNING);
        return -EAGAIN;
    }
    // 处理逻辑
}

YourTestTask::~YourTestTask() {
    unregisterMessageHandlers();
}
```

## 总结

基于任务的消息处理机制成功解决了以下问题：

1. ✅ **移除了isClientNeededByCurrentTest函数** - 不再需要全局判断逻辑
2. ✅ **实现了任务级消息控制** - 测试任务可以精确控制消息处理
3. ✅ **解决了时序问题** - 基于连接信息进行早期过滤
4. ✅ **提供了灵活的扩展机制** - 其他测试任务可以采用相同模式
5. ✅ **保持了高性能** - 事件驱动，减少轮询和无效处理

这个新机制为testd提供了更加灵活、精确和高效的消息处理能力，为复杂测试场景的实现奠定了坚实基础。
