/**
 * @file test_case.hpp
 * @brief 测试用例管理模块头文件 - C++11版本
 */

#ifndef TESTD_TEST_CASE_HPP
#define TESTD_TEST_CASE_HPP

#include "testd.hpp"
#include "task.hpp"

// 前向声明
class MessageHandler;
class MessageFilter;

namespace testd {

class TestCaseListTask;


/**
 * @brief 测试结果类
 */
class TestResult {
public:
    explicit TestResult(const std::string& testId);
    ~TestResult();

    // 禁止拷贝和赋值
    TestResult(const TestResult&) = delete;
    TestResult& operator=(const TestResult&) = delete;

    // 添加案例结果
    void addCaseResult(const std::string& caseName, bool passed, const std::string& message = "");

    void parseCaseListTask(std::shared_ptr<TestCaseListTask> task);

    // 设置测试结束
    void setFinished(int total, int passed, int failed, int durationMs);

    // 保存结果到文件
    bool saveToFile(const std::string& filePath) const;

    // Getters
    const std::string& getTestId() const { return testId; }
    int getTotal() const { return total; }
    int getPassed() const { return passed; }
    int getFailed() const { return failed; }
    int getDurationMs() const { return durationMs; }
    struct json_object* getCaseResults() const { return caseResults; }

    void setTestId(const std::string& id) { testId = id; }

private:
    std::string testId;         /**< 测试ID */
    int total;                  /**< 总测试数 */
    int passed;                 /**< 通过测试数 */
    int failed;                 /**< 失败测试数 */
    int durationMs;             /**< 测试持续时间(毫秒) */
    struct json_object* caseResults; /**< 各案例结果 */
};

/**
 * @brief 测试用例类
 */
class TestCase : public Task {
public:
    TestCase(const std::string& name, const std::string& description, Private);
    static inline std::shared_ptr<TestCase> create(const std::string& name, const std::string& description)
    {
        return std::make_shared<TestCase>(name, description, Private());
    }
    static std::shared_ptr<TestCase> loadFromFile(const std::string& filePath);
    virtual ~TestCase();

    // 禁止拷贝和赋值
    TestCase(const TestCase&) = delete;
    TestCase& operator=(const TestCase&) = delete;

    virtual int executeCustom() override;

    // 从文件加载测试用例

    // Getters
    const std::string& getName() const { return name; }
    const std::string& getDescription() const { return description; }
    struct json_object* getSetup() const { return setup_obj; }
    struct json_object* getCases() const { return cases_obj; }
    struct json_object* getCleanup() const { return cleanup_obj; }
    const std::string& getTestId() const { return testId; }
    std::shared_ptr<TestResult> getResult() { return result; }
    pid_t getTargetPid() const { return targetPid; }
    int getWaitTimerFd() const { return waitTimerFd; }

    // Setters
    void setSetup(struct json_object* s);
    void setCases(struct json_object* c);
    void setCleanup(struct json_object* c);
    void setTargetPid(pid_t pid) { targetPid = pid; }
    void setWaitTimerFd(int fd) { waitTimerFd = fd; }

protected:

    // 生成唯一的测试ID
    static std::string generateTestId();

    std::string name;           /**< 测试用例名称 */
    std::string description;    /**< 测试用例描述 */
    struct json_object* setup_obj;  /**< 设置步骤 */
    struct json_object* cases_obj;  /**< 测试案例列表 */
    struct json_object* cleanup_obj; /**< 清理步骤 */
    std::string testId;         /**< 测试ID */
    std::shared_ptr<TestResult> result;
    pid_t targetPid;            /**< 目标进程ID */
    int caseState;              /**< 案例状态 */

    int waitTimerFd;             /**< 等待计时器fd */
    int sentCount = 0;

    std::weak_ptr<TestCaseListTask> casesTask;

};


class TestSetupTask : public Task {
public:
    TestSetupTask(const std::string& name, const std::string& description, struct json_object* setup_obj, Private);
    static inline std::shared_ptr<TestSetupTask> create(const std::string& name, const std::string& description, struct json_object* setup_obj)
    {
        return std::make_shared<TestSetupTask>(name, description, setup_obj, Private());
    }
    virtual ~TestSetupTask();

    // 禁止拷贝和赋值
    TestSetupTask(const TestSetupTask&) = delete;
    TestSetupTask& operator=(const TestSetupTask&) = delete;

private:
    struct json_object* setup_obj;  /**< 设置步骤 */
};

class TestWaitAttachTask : public Task {
public:
    TestWaitAttachTask(const std::string& name, const std::string& description, std::vector<std::string>& processes, int timeout_ms, Private);
    static inline std::shared_ptr<TestWaitAttachTask> create(const std::string& name, const std::string& description, std::vector<std::string>& processes, int timeout_ms = -1)
    {
        return std::make_shared<TestWaitAttachTask>(name, description, processes, timeout_ms, Private());
    }
    virtual ~TestWaitAttachTask();

    // 禁止拷贝和赋值
    TestWaitAttachTask(const TestWaitAttachTask&) = delete;
    TestWaitAttachTask& operator=(const TestWaitAttachTask&) = delete;

    virtual int executeCustom() override;

private:
    std::vector<std::string> processes;
    int end_time_s, end_time_ms;

    // 消息处理相关
    std::vector<std::string> messageSubscriptionIds_;
    std::vector<std::shared_ptr<ClientConnection>> expectedClients_;

    // 注册init消息处理器
    void registerInitMessageHandlers();
    void unregisterMessageHandlers();
};

class TestWaitTimeTask : public Task {
public:
    TestWaitTimeTask(const std::string& name, const std::string& description, int wait_ms, Private);
    static inline std::shared_ptr<TestWaitTimeTask> create(const std::string& name, const std::string& description, int wait_ms)
    {
        return std::make_shared<TestWaitTimeTask>(name, description, wait_ms, Private());
    }
    virtual ~TestWaitTimeTask();

    // 禁止拷贝和赋值
    TestWaitTimeTask(const TestWaitTimeTask&) = delete;
    TestWaitTimeTask& operator=(const TestWaitTimeTask&) = delete;

    virtual int executeCustom() override;

private:
    int end_time_s, end_time_ms;
};

class TestCaseListTask : public SequentialTask {
public:
    TestCaseListTask(const std::string& name, const std::string& description, struct json_object* case_obj, Private);
    static inline std::shared_ptr<TestCaseListTask> create(const std::string& name, const std::string& description, struct json_object* case_obj)
    {
        return std::make_shared<TestCaseListTask>(name, description, case_obj, Private());
    }
    virtual ~TestCaseListTask();

    // 禁止拷贝和赋值
    TestCaseListTask(const TestCaseListTask&) = delete;
    TestCaseListTask& operator=(const TestCaseListTask&) = delete;

    virtual int executeCustom() override;

private:
    struct json_object* case_obj;
};

class TestCaseTask : public SequentialTask {
public:
    TestCaseTask(const std::string& name, const std::string& description, struct json_object* case_obj, Private);
    static inline std::shared_ptr<TestCaseTask> create(const std::string& name, const std::string& description, struct json_object* case_obj)
    {
        return std::make_shared<TestCaseTask>(name, description, case_obj, Private());
    }
    virtual ~TestCaseTask();

    // 禁止拷贝和赋值
    TestCaseTask(const TestCaseTask&) = delete;
    TestCaseTask& operator=(const TestCaseTask&) = delete;

    virtual int executeCustom() override;

private:
    struct json_object* case_obj;
};

class TestCaseRunTask : public Task {
public:
    TestCaseRunTask(const std::string& name, const std::string& description, struct json_object* case_obj, Private);
    static inline std::shared_ptr<TestCaseRunTask> create(const std::string& name, const std::string& description, struct json_object* case_obj)
    {
        return std::make_shared<TestCaseRunTask>(name, description, case_obj, Private());
    }
    virtual ~TestCaseRunTask();

    // 禁止拷贝和赋值
    TestCaseRunTask(const TestCaseRunTask&) = delete;
    TestCaseRunTask& operator=(const TestCaseRunTask&) = delete;

    virtual int executeCustom() override;

private:
    struct json_object* case_obj;
    std::weak_ptr<ClientConnection> client;
};

class TestCaseVerifyTask : public Task {
public:
    TestCaseVerifyTask(const std::string& name, const std::string& description, struct json_object* verify_obj, Private);
    static inline std::shared_ptr<TestCaseVerifyTask> create(const std::string& name, const std::string& description, struct json_object* verify_obj)
    {
        return std::make_shared<TestCaseVerifyTask>(name, description, verify_obj, Private());
    }
    virtual ~TestCaseVerifyTask();

    // 禁止拷贝和赋值
    TestCaseVerifyTask(const TestCaseVerifyTask&) = delete;
    TestCaseVerifyTask& operator=(const TestCaseVerifyTask&) = delete;

    virtual int executeCustom() override;

private:
    struct json_object* verify_obj;
    bool expect_debug_log;
    std::string log_type, log_message;

    // 命令验证相关
    bool has_command;
    std::string command;
    int expected_exit_code;
    pid_t command_pid;
    bool command_started;
    bool command_finished;
    int actual_exit_code;

    // 消息系统相关
    std::string logSubscriptionId_;
    bool logVerificationPassed_;
    bool logVerificationFailed_;
};

class TestExecTask : public Task {
public:
    TestExecTask(const std::string& name, const std::string& description, const std::vector<std::string>& command, Private);
    static inline std::shared_ptr<TestExecTask> create(const std::string& name, const std::string& description, const std::vector<std::string>& command)
    {
        return std::make_shared<TestExecTask>(name, description, command, Private());
    }
    virtual ~TestExecTask();

    // 禁止拷贝和赋值
    TestExecTask(const TestExecTask&) = delete;
    TestExecTask& operator=(const TestExecTask&) = delete;

    virtual int executeCustom() override;

private:
    std::vector<std::string> command;
    pid_t child_pid;
    int stdout_pipe[2], stderr_pipe[2];

};

} // namespace testd

#endif // TESTD_TEST_CASE_HPP
